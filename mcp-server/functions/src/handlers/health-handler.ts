/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import {RouteHandler} from "../route-handler";
import * as express from "express";

/**
 * Handles health check requests to the server.
 */
export class HealthHandler extends RouteHandler {

    methods(): string[] {
        return ['get'];
    }

    doHandle(_req: express.Request, res: express.Response): void {
        const serverInfo = this.mcpServer.serverInfo();
        res.json({
            status: 'healthy',
            server: serverInfo.name,
            version: serverInfo.version,
            timestamp: new Date().toISOString()
        });
    }
}

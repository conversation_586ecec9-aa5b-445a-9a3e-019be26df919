/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

/**
 * The entry point for the Progressor MCP Server hosted on Firebase Functions.
 *
 * The MCP server enables the Progressor app's integration with generative AI models
 * by handling the requests from LLMs to fetch data from the Progressor Mothership
 * and perform actions on behalf of the user.
 *
 * For more information, see https://modelcontextprotocol.io.
 */

import {onRequest} from "firebase-functions/https";
import express from "express";
import {defineSecret} from "firebase-functions/params";
import {ServerInfoHandler} from "./handlers/server-info-handler";
import {<PERSON>Handler} from "./handlers/health-handler";
import {EncryptTokenHandler} from "./handlers/encrypt-token-handler";
import {PublicUrlHandler} from "./handlers/public-url-handler";
import {McpHandler} from "./handlers/mcp-handler";

/**
 * The name of the secret that stores the secret key used to encrypt and decrypt
 * the Progressor token sent by an LLM.
 */
const mcpServerSecretKey = defineSecret("PROGRESSOR_STARS_MCP_SECRET_KEY");

/**
 * The name of the secret that stores the ngrok auth token.
 */
const ngrokAuthToken = defineSecret("PROGRESSOR_STARS_NGROK_AUTHTOKEN");

/**
 * Configuration constants for the Firebase Functions deployment.
 */
const region = "us-central1";

/**
 * The Express app that handles requests to the MCP server.
 */
const app = express();
app.use(express.json());

/**
 * Register all route handlers with the Express app.
 */
const handlers = [
    new ServerInfoHandler(),
    new HealthHandler(),
    new EncryptTokenHandler(),
    new PublicUrlHandler(),
    new McpHandler()
];

handlers.forEach(handler => handler.registerRoute(app));

export const mcp = onRequest({
    maxInstances: 5,
    region: region,
    secrets: [mcpServerSecretKey, ngrokAuthToken],
}, app);

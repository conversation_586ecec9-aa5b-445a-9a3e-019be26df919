/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import * as fs from "fs";
import * as logger from "firebase-functions/logger";
import {randomUUID} from "crypto";
import * as ngrok from "@ngrok/ngrok";

export class Tunnel {

    private readonly publicHostFile = ".publichost";

    private readonly authTokenProvider: () => string;
    private readonly port: number;

    constructor(authTokenProvider: () => string, port: number) {
        this.authTokenProvider = authTokenProvider;
        this.port = port;
    }

    async publicHost(): Promise<string> {
        let host = this.readHostFromFile();

        if (host) {
            logger.info(`Returning existing host: ${host}`);
            return host;
        }

        logger.info("No existing host found, opening new tunnel...");
        host = await this.open();

        return host;
    }

    private async open(): Promise<string> {
        const shortId = randomUUID().slice(0, 8);
        const domain = `mcp-server-${shortId}.ngrok.app`;

        const authToken = this.authTokenProvider();
        if (!authToken) {
            throw new Error("The `PROGRESSOR_STARS_NGROK_AUTHTOKEN` secret is not set.");
        }

        logger.info(`Starting ngrok tunnel on port ${this.port} with domain ${domain}...`);

        const listener = await ngrok.forward({
            authtoken: authToken,
            addr: this.port,
            domain: domain,
        });

        const host = listener.url()!.replace(/^https?:\/\//, '');

        logger.info(`Tunnel opened, host: ${host}`);
        this.writeHostToFile(host);

        return host;
    }

    private readHostFromFile(): string | null {
        try {
            if (fs.existsSync(this.publicHostFile)) {
                const content = fs.readFileSync(this.publicHostFile, "utf8").trim();
                return content || null;
            }
            return null;
        } catch (error) {
            logger.error("Error reading public host file:", error);
            return null;
        }
    }

    private writeHostToFile(host: string): void {
        try {
            fs.writeFileSync(this.publicHostFile, host, "utf8");
        } catch (error) {
            logger.error("Error writing public host file:", error);
            throw error;
        }
    }
}

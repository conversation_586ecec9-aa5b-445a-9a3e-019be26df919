/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import * as express from "express";
import {defineSecret} from "firebase-functions/params";
import {McpServer} from "./mcp/mcp-server";
import {Crypto} from "./crypto/crypto";
import {Tunnel} from "./ngrok/tunnel";

const mcpServerSecretKey = defineSecret('PROGRESSOR_STARS_MCP_SECRET_KEY');
const ngrokAuthToken = defineSecret('PROGRESSOR_STARS_NGROK_AUTHTOKEN');

/**
 * Base class for handlers that handle specific sub-routes
 * within the `mcpServer` Firebase Function.
 */
export abstract class RouteHandler {

    protected readonly mcpServer: McpServer;
    protected readonly crypto: Crypto;
    protected readonly tunnel: Tunnel;

    constructor() {
        this.mcpServer = new McpServer(() => mcpServerSecretKey.value());
        this.crypto = new Crypto(() => mcpServerSecretKey.value());
        this.tunnel = new Tunnel(() => ngrokAuthToken.value(), 5002);
    }

    /**
     * Registers this route handler with the Express application.
     *
     * @param app the Express application to register the route with.
     */
    abstract registerRoute(app: express.Application): void;
}

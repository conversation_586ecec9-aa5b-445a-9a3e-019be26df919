/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import * as express from "express";
import {defineSecret} from "firebase-functions/params";
import {McpServer} from "./mcp/mcp-server";
import {Crypto} from "./crypto/crypto";
import {Tunnel} from "./ngrok/tunnel";

const mcpServerSecretKey = defineSecret('PROGRESSOR_STARS_MCP_SECRET_KEY');
const ngrokAuthToken = defineSecret('PROGRESSOR_STARS_NGROK_AUTHTOKEN');

/**
 * Base class for handlers that handle specific sub-routes
 * within the `mcpServer` Firebase Function.
 */
export abstract class RouteHandler {

    protected readonly mcpServer: McpServer;
    protected readonly crypto: Crypto;
    protected readonly tunnel: Tunnel;
    protected readonly route: string;

    constructor(route: string) {
        this.route = route;
        this.mcpServer = new McpServer(() => mcpServerSecretKey.value());
        this.crypto = new Crypto(() => mcpServerSecretKey.value());
        this.tunnel = new Tunnel(() => ngrokAuthToken.value(), 5002);
    }

    /**
     * Returns the HTTP methods that this handler supports.
     */
    abstract methods(): string[];

    /**
     * Handles the actual request processing logic.
     *
     * @param req the Express request object.
     * @param res the Express response object.
     */
    abstract doHandle(req: express.Request, res: express.Response): void | Promise<void>;

    /**
     * Registers this route handler with the Express application.
     *
     * @param app the Express application to register the route with.
     */
    registerRoute(app: express.Application): void {
        this.methods().forEach(method => {
            const methodLower = method.toLowerCase();
            if (methodLower === 'all') {
                app.all(this.route, this.doHandle.bind(this));
            } else {
                (app as any)[methodLower](this.route, this.doHandle.bind(this));
            }
        });
    }
}
